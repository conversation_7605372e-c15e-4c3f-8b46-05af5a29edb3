# 🔌 دليل API الوهمي - MikroTik Card Generator

## 📋 نظرة عامة

تم إضافة **API وهمي** كامل لمحاكاة الاتصال براوتر MikroTik الحقيقي. هذا يتيح لك تجربة جميع وظائف التطبيق بدون الحاجة لراوتر فعلي.

## ✨ المميزات الجديدة

### 🔗 **الاتصال بالراوتر:**
- محاكاة اتصال حقيقي مع تأخير الشبكة
- التحقق من صحة عنوان IP
- التحقق من بيانات الاعتماد
- احتمال فشل عشوائي (10%) لمحاكاة مشاكل الشبكة الحقيقية

### 👥 **إدارة المستخدمين:**
- إضافة مستخدمين جدد للراوتر
- عرض قائمة المستخدمين الموجودين
- حذف مستخدمين
- إضافة عدة مستخدمين دفعة واحدة

### 📊 **معلومات الراوتر:**
- عرض معلومات النظام
- إحصائيات الأداء
- معلومات الذاكرة والمعالج
- وقت التشغيل

## 🎮 كيفية الاستخدام

### 1. **الاتصال بالراوتر:**
```
عنوان IP: أي عنوان IP صحيح (مثل: ***********)
اسم المستخدم: أي اسم (مثل: admin)
كلمة المرور: أي كلمة مرور (مثل: admin123)
```

### 2. **الأزرار الجديدة:**
- **🔗 الاتصال بالراوتر**: يتصل بالراوتر الوهمي
- **ℹ️ معلومات الراوتر**: يعرض معلومات النظام
- **👥 عرض المستخدمين**: يعرض قائمة المستخدمين
- **❌ قطع الاتصال**: يقطع الاتصال

### 3. **توليد وإضافة الكروت:**
1. اتصل بالراوتر أولاً
2. أدخل إعدادات الكروت
3. اضغط "توليد الكروت"
4. سيتم إضافة المستخدمين تلقائياً للراوتر

## 🔧 التفاصيل التقنية

### **فئة MockMikrotikApi:**
```java
// الاتصال
boolean connect(String ip, String username, String password)

// إضافة مستخدم واحد
boolean addPppUser(String username, String password, String profile)

// إضافة عدة مستخدمين
int addMultiplePppUsers(List<String> credentials, String profile)

// الحصول على المستخدمين
List<Map<String, String>> getPppUsers()

// معلومات الراوتر
Map<String, String> getRouterInfo()

// حذف مستخدم
boolean deletePppUser(String username)

// قطع الاتصال
void disconnect()
```

### **البيانات الوهمية:**
- **مستخدمون موجودون مسبقاً**: admin, user1, test
- **معلومات راوتر**: RouterBOARD 750Gr3, RouterOS 6.49.6
- **إحصائيات عشوائية**: استخدام المعالج، الذاكرة، وقت التشغيل

## 🎯 سيناريوهات الاختبار

### ✅ **اختبار الاتصال الناجح:**
```
IP: ***********
Username: admin
Password: admin123
النتيجة: اتصال ناجح
```

### ❌ **اختبار فشل الاتصال:**
```
IP: (فارغ)
Username: admin
Password: admin123
النتيجة: "عنوان IP غير صحيح"
```

### 👥 **اختبار إضافة المستخدمين:**
1. اتصل بالراوتر
2. ولّد 5 كروت
3. تحقق من إضافتهم بـ "عرض المستخدمين"

### 📊 **اختبار معلومات الراوتر:**
1. اتصل بالراوتر
2. اضغط "معلومات الراوتر"
3. ستظهر معلومات النظام في منطقة النتائج

## 🔄 محاكاة الواقع

### **تأخير الشبكة:**
- الاتصال: 1-3 ثواني
- إضافة مستخدم: 200-500 مللي ثانية
- استعلام البيانات: 300-800 مللي ثانية

### **أخطاء محتملة:**
- فشل الاتصال العشوائي (10%)
- مستخدم موجود مسبقاً
- عنوان IP غير صحيح
- بيانات اعتماد فارغة

### **حالات الاستخدام:**
- ✅ إضافة مستخدمين جدد
- ✅ عرض المستخدمين الموجودين
- ✅ معلومات النظام
- ✅ إحصائيات الأداء
- ✅ قطع الاتصال

## 🚀 المميزات المتقدمة

### **إدارة الحالة:**
- تتبع حالة الاتصال
- تفعيل/تعطيل الأزرار حسب الحالة
- حفظ بيانات المستخدمين في الذاكرة

### **التحقق من البيانات:**
- فحص صحة عنوان IP
- منع إضافة مستخدمين مكررين
- التحقق من بيانات الاعتماد

### **واجهة المستخدم:**
- تحديث ألوان الأزرار حسب الحالة
- رسائل واضحة للمستخدم
- عرض النتائج في منطقة مخصصة

## 📱 تجربة المستخدم

### **الشاشة الرئيسية:**
```
┌─────────────────────────────────┐
│        إعدادات الراوتر          │
├─────────────────────────────────┤
│ IP: [***********            ] │
│ Username: [admin             ] │
│ Password: [••••••••••        ] │
├─────────────────────────────────┤
│ [الاتصال] [معلومات الراوتر]    │
│ [عرض المستخدمين] [قطع الاتصال] │
├─────────────────────────────────┤
│      إعدادات توليد الكروت       │
│ ...                            │
└─────────────────────────────────┘
```

### **تدفق العمل:**
1. **إدخال بيانات الراوتر** → 
2. **الاتصال** → 
3. **توليد الكروت** → 
4. **إضافة تلقائية للراوتر** → 
5. **عرض النتائج**

## 🔮 التطوير المستقبلي

### **مميزات مخططة:**
- 🔄 مزامنة مع راوتر حقيقي
- 📈 إحصائيات متقدمة
- 🗂️ ملفات شخصية متعددة
- 🔒 إدارة الصلاحيات
- 📊 تقارير الاستخدام

### **تحسينات تقنية:**
- استبدال AsyncTask بـ ExecutorService
- إضافة Retrofit للـ API الحقيقي
- تحسين إدارة الذاكرة
- إضافة اختبارات وحدة

---

## ✅ الخلاصة

**API الوهمي** يوفر تجربة كاملة ومتكاملة لاختبار جميع وظائف التطبيق:

- 🔗 **اتصال واقعي** مع تأخير وأخطاء محتملة
- 👥 **إدارة مستخدمين** كاملة
- 📊 **معلومات نظام** مفصلة
- 🎮 **واجهة تفاعلية** سهلة الاستخدام

**التطبيق الآن جاهز للاستخدام الكامل! 🚀**
