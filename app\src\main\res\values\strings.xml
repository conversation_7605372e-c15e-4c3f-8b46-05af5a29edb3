<resources>
    <string name="app_name">MikrotikCardGenerator</string>

    <!-- String arrays -->
    <string-array name="cred_types">
        <item>أرقام وحروف</item>
        <item>أرقام فقط</item>
        <item>حروف فقط</item>
    </string-array>

    <!-- UI Strings -->
    <string name="router_ip_hint">عنوان IP للراوتر</string>
    <string name="router_username_hint">اسم مستخدم الراوتر</string>
    <string name="router_password_hint">كلمة مرور الراوتر</string>
    <string name="connect_to_router">الاتصال بالراوتر</string>
    <string name="prefix_hint">البادئة</string>
    <string name="suffix_hint">اللاحقة</string>
    <string name="length_hint">طول الكود</string>
    <string name="count_hint">عدد الكروت</string>
    <string name="cred_type_hint">نوع البيانات</string>
    <string name="generate_cards">توليد الكروت</string>
    <string name="output_label">النتائج:</string>
    <string name="view_saved_cards">عرض الكروت المحفوظة</string>
    <string name="export_pdf">تصدير PDF</string>
</resources>