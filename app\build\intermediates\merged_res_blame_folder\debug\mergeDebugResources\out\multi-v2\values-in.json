{"logs": [{"outputFile": "com.example.mikrotikcardgenerator.app-mergeDebugResources-26:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7d8d788490da515ed5703e42d0a5a29\\transformed\\material-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2762,2815,2867,2933,3005,3089,3172,3247,3323,3396,3471,3556,3631,3723,3817,3891,3964,4058,4110,4179,4264,4351,4413,4477,4540,4643,4743,4838,4940,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2757,2810,2862,2928,3000,3084,3167,3242,3318,3391,3466,3551,3626,3718,3812,3886,3959,4053,4105,4174,4259,4346,4408,4472,4535,4638,4738,4833,4935,4992,5048,5128"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3102,3178,3257,3347,3432,3538,3654,3737,3802,3896,3961,4020,4107,4169,4231,4291,4357,4419,4473,4585,4642,4703,4757,4829,4955,5041,5125,5264,5345,5426,5516,5569,5621,5687,5759,5843,5926,6001,6077,6150,6225,6310,6385,6477,6571,6645,6718,6812,6864,6933,7018,7105,7167,7231,7294,7397,7497,7592,7694,7751,7807", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "314,3097,3173,3252,3342,3427,3533,3649,3732,3797,3891,3956,4015,4102,4164,4226,4286,4352,4414,4468,4580,4637,4698,4752,4824,4950,5036,5120,5259,5340,5421,5511,5564,5616,5682,5754,5838,5921,5996,6072,6145,6220,6305,6380,6472,6566,6640,6713,6807,6859,6928,7013,7100,7162,7226,7289,7392,7492,7587,7689,7746,7802,7882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ef7928f296f3a9fc53086a6c41b89e4\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "7972", "endColumns": "100", "endOffsets": "8068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1cf9ab8460bf2fdf2e92c9407230975\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,643,730,834,950,1033,1111,1202,1295,1390,1484,1584,1677,1772,1866,1957,2048,2134,2237,2342,2443,2547,2656,2764,2924,7887", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,638,725,829,945,1028,1106,1197,1290,1385,1479,1579,1672,1767,1861,1952,2043,2129,2232,2337,2438,2542,2651,2759,2919,3018,7967"}}]}]}