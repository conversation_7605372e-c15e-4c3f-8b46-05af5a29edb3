# 🔧 حل مشكلة Android Studio - Activity class does not exist

## 🚨 المشكلة:
```
Error running 'app'
Activity class {com.lawoffice.mikrotikcardgenerator/com.example.mikrotikcardgenerator.MainActivity} does not exist
```

## ✅ الحل الكامل:

### الخطوة 1: إغلاق Android Studio
```
أغلق Android Studio تماماً
```

### الخطوة 2: تنظيف المشروع من Terminal/Command Prompt
```bash
cd C:\Users\<USER>\MikrotikCardGenerator
.\gradlew clean
```

### الخطوة 3: حذف ملفات الكاش
```bash
# حذف مجلدات البناء
Remove-Item -Recurse -Force "build" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force "app\build" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force ".gradle" -ErrorAction SilentlyContinue

# حذف إعدادات التشغيل القديمة
Remove-Item -Recurse -Force ".idea\runConfigurations" -ErrorAction SilentlyContinue
```

### الخطوة 4: إعادة فتح Android Studio
1. افتح Android Studio
2. اختر "Open an existing project"
3. اختر مجلد `C:\Users\<USER>\MikrotikCardGenerator`
4. انتظر حتى ينتهي Gradle Sync

### الخطوة 5: إنشاء Run Configuration جديدة
1. اذهب إلى **Run** → **Edit Configurations**
2. احذف أي configuration موجودة للتطبيق
3. اضغط **+** → **Android App**
4. اسم Configuration: `app`
5. Module: `MikrotikCardGenerator.app.main`
6. Launch Activity: **Specified Activity**
7. Activity: `com.example.mikrotikcardgenerator.MainActivity`
8. اضغط **Apply** ثم **OK**

### الخطوة 6: التحقق من الإعدادات
تأكد من أن:
- **Package name**: `com.example.mikrotikcardgenerator`
- **MainActivity** موجودة في: `app/src/main/java/com/example/mikrotikcardgenerator/MainActivity.java`

---

## 🔄 طريقة بديلة - إعادة إنشاء المشروع:

### إذا لم تنجح الطريقة الأولى:

1. **إنشاء مشروع جديد:**
   - File → New → New Project
   - اختر "Empty Activity"
   - Package name: `com.example.mikrotikcardgenerator`
   - اسم المشروع: `MikrotikCardGenerator`

2. **نسخ الملفات:**
   ```bash
   # انسخ هذه الملفات للمشروع الجديد:
   app/src/main/java/com/example/mikrotikcardgenerator/MainActivity.java
   app/src/main/java/com/example/mikrotikcardgenerator/SavedCardsActivity.java
   app/src/main/res/layout/activity_main.xml
   app/src/main/res/layout/activity_saved_cards.xml
   app/src/main/res/values/strings.xml
   app/build.gradle.kts
   ```

3. **تحديث AndroidManifest.xml:**
   ```xml
   <activity
       android:name=".SavedCardsActivity"
       android:exported="false"
       android:parentActivityName=".MainActivity" />
   ```

---

## 📱 طرق التشغيل البديلة:

### الطريقة 1: تثبيت APK مباشرة
```bash
# من Terminal في مجلد المشروع
.\gradlew installDebug

# أو
adb install app/build/outputs/apk/debug/app-debug.apk
```

### الطريقة 2: تشغيل من Command Line
```bash
# بناء وتثبيت وتشغيل
.\gradlew installDebug
adb shell am start -n com.example.mikrotikcardgenerator/.MainActivity
```

### الطريقة 3: نسخ APK للجهاز
1. انسخ: `app/build/outputs/apk/debug/app-debug.apk`
2. انقله للجهاز
3. فعّل "Unknown Sources"
4. ثبّت الملف

---

## 🔍 التحقق من نجاح الحل:

### في Android Studio:
1. **Project Structure** يجب أن يظهر:
   ```
   app/src/main/java/com/example/mikrotikcardgenerator/
   ├── MainActivity.java
   └── SavedCardsActivity.java
   ```

2. **Run Configuration** يجب أن تظهر:
   - Module: `MikrotikCardGenerator.app.main`
   - Activity: `com.example.mikrotikcardgenerator.MainActivity`

3. **Build** يجب أن ينجح:
   ```
   BUILD SUCCESSFUL in Xs
   ```

### عند التشغيل:
- يجب أن يفتح التطبيق بدون أخطاء
- تظهر الشاشة الرئيسية مع جميع العناصر
- يمكن توليد الكروت بنجاح

---

## 🚨 مشاكل شائعة إضافية:

### 1. Gradle Sync Failed
```bash
.\gradlew clean
.\gradlew build --refresh-dependencies
```

### 2. Module not found
- File → Invalidate Caches and Restart
- اختر "Invalidate and Restart"

### 3. SDK Issues
- File → Project Structure → SDK Location
- تأكد من مسار Android SDK

### 4. Build Tools Issues
- SDK Manager → SDK Tools
- تأكد من تثبيت Build Tools 33.0.0

---

## ✅ النتيجة المتوقعة:

بعد تطبيق هذه الخطوات:
- ✅ Android Studio يتعرف على المشروع بشكل صحيح
- ✅ Run Configuration تعمل بدون أخطاء
- ✅ التطبيق يعمل على الجهاز/المحاكي
- ✅ جميع الوظائف تعمل (توليد كروت، PDF، قاعدة بيانات)

---

## 📞 إذا استمرت المشكلة:

1. **تحقق من Logcat:**
   ```bash
   adb logcat | grep -i mikrotikcardgenerator
   ```

2. **تحقق من Package Name:**
   ```bash
   aapt dump badging app/build/outputs/apk/debug/app-debug.apk | grep package
   ```

3. **إعادة تشغيل ADB:**
   ```bash
   adb kill-server
   adb start-server
   ```

**المشروع جاهز للعمل! 🎉**
