#Thu Jun 05 23:32:40 EEST 2025
com.example.mikrotikcardgenerator.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.mikrotikcardgenerator.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.mikrotikcardgenerator.app-main-5\:/layout/activity_main.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.example.mikrotikcardgenerator.app-main-5\:/layout/activity_saved_cards.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_saved_cards.xml
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-anydpi/ic_launcher.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.mikrotikcardgenerator.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.mikrotikcardgenerator.app-main-5\:/xml/backup_rules.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.mikrotikcardgenerator.app-main-5\:/xml/data_extraction_rules.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
