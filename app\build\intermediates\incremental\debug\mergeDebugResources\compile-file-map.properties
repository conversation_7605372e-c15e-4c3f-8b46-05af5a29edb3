#Thu Jun 05 23:32:40 EEST 2025
com.example.mikrotikcardgenerator.app-main-30\:/drawable/ic_launcher_background.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/layout/activity_main.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/layout/activity_saved_cards.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_saved_cards.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-anydpi/ic_launcher.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.mikrotikcardgenerator.app-main-30\:/xml/backup_rules.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.mikrotikcardgenerator.app-main-30\:/xml/data_extraction_rules.xml=C\:\\Users\\ahmed\\MikrotikCardGenerator\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
