com.example.mikrotikcardgenerator.app-drawerlayout-1.1.1-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07837f2e8156f753da4c078e03a0a781\transformed\drawerlayout-1.1.1\res
com.example.mikrotikcardgenerator.app-core-ktx-1.9.0-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b22b004ba07b68915d7135daea7daeb\transformed\core-ktx-1.9.0\res
com.example.mikrotikcardgenerator.app-recyclerview-1.1.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4f9f1d26b1b426647aa9466e2b378d\transformed\recyclerview-1.1.0\res
com.example.mikrotikcardgenerator.app-activity-1.6.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\349b87b933a1709b20a6463c5e761f21\transformed\activity-1.6.0\res
com.example.mikrotikcardgenerator.app-appcompat-resources-1.6.1-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\492e97e74311f317167b8c7315ddb3e3\transformed\appcompat-resources-1.6.1\res
com.example.mikrotikcardgenerator.app-lifecycle-viewmodel-2.5.1-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5573549de6a6df3798d06db555897044\transformed\lifecycle-viewmodel-2.5.1\res
com.example.mikrotikcardgenerator.app-constraintlayout-2.1.4-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5acc1f01c634a190d2cd8f2a0386606b\transformed\constraintlayout-2.1.4\res
com.example.mikrotikcardgenerator.app-emoji2-1.2.0-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\res
com.example.mikrotikcardgenerator.app-emoji2-views-helper-1.2.0-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9bc7d76e9a9faf5ccff54f7f15a635\transformed\emoji2-views-helper-1.2.0\res
com.example.mikrotikcardgenerator.app-transition-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d0ce2bfc865f5b0dcad00d448a3e47c\transformed\transition-1.2.0\res
com.example.mikrotikcardgenerator.app-cardview-1.0.0-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d875b1c0926ce30f0c2c140ec9a416f\transformed\cardview-1.0.0\res
com.example.mikrotikcardgenerator.app-coordinatorlayout-1.1.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c3cf3ea01e42faaf84f49914b8ccb32\transformed\coordinatorlayout-1.1.0\res
com.example.mikrotikcardgenerator.app-savedstate-1.2.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fbd3e0c1df71bc105bebe5025a0f98c\transformed\savedstate-1.2.0\res
com.example.mikrotikcardgenerator.app-core-1.9.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\res
com.example.mikrotikcardgenerator.app-lifecycle-runtime-2.5.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f5b9da444d065613045ab5337ab1387\transformed\lifecycle-runtime-2.5.1\res
com.example.mikrotikcardgenerator.app-lifecycle-livedata-core-2.5.1-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97339f09c362cd857940d8009a62de33\transformed\lifecycle-livedata-core-2.5.1\res
com.example.mikrotikcardgenerator.app-fragment-1.3.6-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7a7f93fd9d15f2704d8ccad538ed6dc\transformed\fragment-1.3.6\res
com.example.mikrotikcardgenerator.app-material-1.9.0-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7d8d788490da515ed5703e42d0a5a29\transformed\material-1.9.0\res
com.example.mikrotikcardgenerator.app-startup-runtime-1.1.1-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf37978e2ace7eb5792125215edb1520\transformed\startup-runtime-1.1.1\res
com.example.mikrotikcardgenerator.app-lifecycle-process-2.4.1-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07881218a44f82065948a6f2831db9b\transformed\lifecycle-process-2.4.1\res
com.example.mikrotikcardgenerator.app-lifecycle-viewmodel-savedstate-2.5.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c34fafbd7f4b16d62097b6e22831abc0\transformed\lifecycle-viewmodel-savedstate-2.5.1\res
com.example.mikrotikcardgenerator.app-appcompat-1.6.1-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1cf9ab8460bf2fdf2e92c9407230975\transformed\appcompat-1.6.1\res
com.example.mikrotikcardgenerator.app-annotation-experimental-1.3.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6b1eb7f79aa04bba5918cc6f99116a5\transformed\annotation-experimental-1.3.0\res
com.example.mikrotikcardgenerator.app-viewpager2-1.0.0-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb7fe7d92eb9013764c917827ccaa562\transformed\viewpager2-1.0.0\res
com.example.mikrotikcardgenerator.app-pngs-24 C:\Users\<USER>\MikrotikCardGenerator\app\build\generated\res\pngs\debug
com.example.mikrotikcardgenerator.app-resValues-25 C:\Users\<USER>\MikrotikCardGenerator\app\build\generated\res\resValues\debug
com.example.mikrotikcardgenerator.app-packageDebugResources-26 C:\Users\<USER>\MikrotikCardGenerator\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.mikrotikcardgenerator.app-packageDebugResources-27 C:\Users\<USER>\MikrotikCardGenerator\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.mikrotikcardgenerator.app-debug-28 C:\Users\<USER>\MikrotikCardGenerator\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.mikrotikcardgenerator.app-debug-29 C:\Users\<USER>\MikrotikCardGenerator\app\src\debug\res
com.example.mikrotikcardgenerator.app-main-30 C:\Users\<USER>\MikrotikCardGenerator\app\src\main\res
