# 🚀 دليل البدء السريع - MikroTik Card Generator

## 📱 تشغيل التطبيق

### الطريقة الأولى - Android Studio:
1. افتح Android Studio
2. افتح المشروع من `C:\Users\<USER>\MikrotikCardGenerator`
3. انتظر Gradle Sync
4. اضغط Run (▶️)

### الطريقة الثانية - APK مباشر:
```bash
.\gradlew installDebug
```

### الطريقة الثالثة - نسخ يدوي:
1. انسخ `app\build\outputs\apk\debug\app-debug.apk`
2. انقله للجهاز وثبّته

---

## 🎯 اختبار سريع (5 دقائق)

### الخطوة 1: اختبار الاتصال
```
📍 IP: ***********
👤 Username: admin  
🔐 Password: admin123
➡️ اضغط "الاتصال بالراوتر"
✅ يجب أن يظهر "متصل ✓"
```

### الخطوة 2: عرض معلومات الراوتر
```
➡️ اضغط "معلومات الراوتر"
✅ ستظهر معلومات النظام في منطقة النتائج
```

### الخطوة 3: توليد كروت
```
🏷️ البادئة: user
🏷️ اللاحقة: 01
📏 طول الكود: 6
🔢 عدد الكروت: 5
📋 نوع البيانات: أرقام وحروف
➡️ اضغط "توليد الكروت"
✅ ستظهر 5 كروت مع كلمات مرور
✅ رسالة "تم إضافة 5 مستخدم إلى الراوتر"
```

### الخطوة 4: عرض المستخدمين
```
➡️ اضغط "عرض المستخدمين"
✅ ستظهر قائمة تشمل المستخدمين الجدد
```

### الخطوة 5: تصدير PDF
```
➡️ اضغط "تصدير PDF"
✅ رسالة مع مسار الملف المحفوظ
📁 تحقق من مجلد MikrotikCards
```

---

## 🔧 استكشاف الأخطاء

### ❌ "Activity class does not exist"
```bash
# الحل:
.\gradlew clean
.\gradlew assembleDebug
# أعد تشغيل Android Studio
```

### ❌ "Build failed"
```bash
# تنظيف شامل:
Remove-Item -Recurse -Force "build"
Remove-Item -Recurse -Force "app\build"
Remove-Item -Recurse -Force ".gradle"
.\gradlew assembleDebug
```

### ❌ "فشل الاتصال"
```
✅ هذا طبيعي! API الوهمي يفشل أحياناً (10%)
➡️ أعد المحاولة
```

---

## 📋 قائمة التحقق

### ✅ التطبيق يعمل:
- [ ] يفتح بدون أخطاء
- [ ] جميع الحقول ظاهرة
- [ ] الأزرار تستجيب

### ✅ الاتصال يعمل:
- [ ] يقبل أي IP صحيح
- [ ] يظهر "متصل ✓"
- [ ] الأزرار الإضافية تفعل

### ✅ توليد الكروت يعمل:
- [ ] يولد كروت بالعدد المطلوب
- [ ] يظهر النتائج في المنطقة النصية
- [ ] يحفظ في قاعدة البيانات

### ✅ المميزات الإضافية:
- [ ] معلومات الراوتر تظهر
- [ ] قائمة المستخدمين تظهر
- [ ] تصدير PDF يعمل
- [ ] عرض الكروت المحفوظة يعمل

---

## 🎮 سيناريوهات الاختبار

### 🟢 السيناريو الأساسي:
1. اتصل بالراوتر
2. ولّد 3 كروت
3. اعرض المستخدمين
4. صدّر PDF
5. اعرض الكروت المحفوظة

### 🟡 سيناريو الأخطاء:
1. حاول الاتصال ببيانات فارغة
2. حاول توليد كروت بدون اتصال
3. حاول تصدير PDF بدون كروت

### 🔵 سيناريو متقدم:
1. اتصل بالراوتر
2. اعرض معلومات الراوتر
3. ولّد 10 كروت بأنواع مختلفة
4. اعرض المستخدمين الجدد
5. اقطع الاتصال
6. أعد الاتصال

---

## 📊 النتائج المتوقعة

### 🔗 عند الاتصال الناجح:
```
✅ زر "متصل ✓" باللون الأخضر
✅ تفعيل أزرار: معلومات الراوتر، عرض المستخدمين، قطع الاتصال
✅ رسالة "تم الاتصال بالراوتر بنجاح"
```

### 📊 معلومات الراوتر:
```
=== معلومات الراوتر ===

الموديل: RouterBOARD 750Gr3
الإصدار: 6.49.6 (stable)
وقت التشغيل: 2w3d4h15m30s
استخدام المعالج: 25%
الذاكرة المتاحة: 768 MB
إجمالي الذاكرة: 1024 MB
```

### 👥 قائمة المستخدمين:
```
=== مستخدمو الراوتر ===

العدد الإجمالي: 8 مستخدم

الاسم: admin
الملف الشخصي: default
الحالة: مفعل
ملاحظة: مستخدم موجود مسبقاً
------------------------
```

### 🎯 توليد الكروت:
```
user1a2b3c01 : Kj8mN2pQ
user4d5e6f01 : Lm9oP3qR
user7g8h9i01 : Nn0sT4uV
user2j3k4l01 : Pp1wX5yZ
user5m6n7o01 : Qq2aB6cD
```

---

## 🚀 نصائح للاستخدام الأمثل

### 💡 **نصائح عامة:**
- استخدم أرقام صغيرة للاختبار (1-10 كروت)
- جرب أنواع البيانات المختلفة
- تحقق من مجلد MikrotikCards للملفات المصدرة

### 🔧 **نصائح تقنية:**
- أعد تشغيل التطبيق إذا واجهت مشاكل
- استخدم "تنظيف البيانات" إذا تعطلت قاعدة البيانات
- تحقق من مساحة التخزين قبل تصدير PDF

### 📱 **نصائح الواجهة:**
- استخدم التمرير لرؤية جميع العناصر
- اضغط مطولاً على النصوص لنسخها
- استخدم زر الرجوع للعودة من الشاشات الفرعية

---

## ✅ التأكد من النجاح

إذا تمكنت من:
- ✅ الاتصال بالراوتر الوهمي
- ✅ توليد كروت وإضافتها للراوتر
- ✅ عرض معلومات الراوتر والمستخدمين
- ✅ تصدير PDF بنجاح
- ✅ عرض الكروت المحفوظة

**🎉 مبروك! التطبيق يعمل بشكل مثالي!**

---

## 📞 المساعدة

إذا واجهت مشاكل:
1. راجع `TROUBLESHOOTING.md`
2. راجع `ANDROID_STUDIO_FIX.md`
3. راجع `MOCK_API_GUIDE.md`

**التطبيق جاهز للاستخدام الكامل! 🚀**
