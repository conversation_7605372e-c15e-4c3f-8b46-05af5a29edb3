package com.example.mikrotikcardgenerator;

import android.content.ContentValues;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;

// import me.legrange.mikrotik.ApiConnection;
// import me.legrange.mikrotik.MikrotikApiException;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class MainActivity extends AppCompatActivity {

    private EditText routerIpInput, routerUsernameInput, routerPasswordInput;
    private EditText prefixInput, suffixInput, lengthInput, countInput;
    private Spinner credTypeSpinner;
    private Button connectButton, generateButton, viewSavedButton, exportPdfButton;
    private TextView outputText;
    private DatabaseHelper dbHelper;
    // private ApiConnection connection;
    private boolean isConnectedToRouter = false;
    private ArrayList<String> lastGeneratedCredentials;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // ربط عناصر الواجهة
        routerIpInput = findViewById(R.id.router_ip);
        routerUsernameInput = findViewById(R.id.router_username);
        routerPasswordInput = findViewById(R.id.router_password);
        prefixInput = findViewById(R.id.prefix_input);
        suffixInput = findViewById(R.id.suffix_input);
        lengthInput = findViewById(R.id.length_input);
        countInput = findViewById(R.id.count_input);
        credTypeSpinner = findViewById(R.id.cred_type_spinner);
        connectButton = findViewById(R.id.connect_button);
        generateButton = findViewById(R.id.generate_button);
        viewSavedButton = findViewById(R.id.view_saved_button);
        exportPdfButton = findViewById(R.id.export_pdf_button);
        outputText = findViewById(R.id.output_text);

        // إعداد قاعدة البيانات
        dbHelper = new DatabaseHelper(this);
        lastGeneratedCredentials = new ArrayList<>();

        // إعداد القائمة المنسدلة لنوع البيانات
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                R.array.cred_types, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        credTypeSpinner.setAdapter(adapter);

        // إعداد أزرار الواجهة
        setupButtonListeners();
    }

    private void setupButtonListeners() {
        connectButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                connectToRouter();
            }
        });

        generateButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                generateCredentials();
            }
        });

        viewSavedButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                viewSavedCards();
            }
        });

        exportPdfButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                exportToPdf();
            }
        });
    }

    // دالة للاتصال بالراوتر
    private void connectToRouter() {
        String ip = routerIpInput.getText().toString().trim();
        String username = routerUsernameInput.getText().toString().trim();
        String password = routerPasswordInput.getText().toString().trim();

        if (ip.isEmpty() || username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "يرجى إدخال جميع بيانات الراوتر", Toast.LENGTH_SHORT).show();
            return;
        }

        new ConnectToRouterTask().execute(ip, username, password);
    }

    // دالة لتوليد بيانات الاعتماد
    private void generateCredentials() {
        try {
            String prefix = prefixInput.getText().toString().trim();
            String suffix = suffixInput.getText().toString().trim();
            String lengthStr = lengthInput.getText().toString().trim();
            String countStr = countInput.getText().toString().trim();

            if (lengthStr.isEmpty() || countStr.isEmpty()) {
                Toast.makeText(this, "يرجى إدخال طول الكود وعدد الكروت", Toast.LENGTH_SHORT).show();
                return;
            }

            int length = Integer.parseInt(lengthStr);
            int count = Integer.parseInt(countStr);
            String credType = credTypeSpinner.getSelectedItem().toString();

            if (length < 3 || count < 1 || count > 1000) {
                Toast.makeText(this, "يرجى إدخال قيم صحيحة (طول الكود >= 3، عدد الكروت 1-1000)", Toast.LENGTH_SHORT).show();
                return;
            }

            lastGeneratedCredentials.clear();
            ArrayList<String> credentials = new ArrayList<>();
            Random random = new Random();

            for (int i = 0; i < count; i++) {
                String characters;
                switch (credType) {
                    case "أرقام فقط":
                        characters = "0123456789";
                        break;
                    case "حروف فقط":
                        characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
                        break;
                    default: // أرقام وحروف
                        characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
                        break;
                }

                StringBuilder base = new StringBuilder();
                for (int j = 0; j < length; j++) {
                    base.append(characters.charAt(random.nextInt(characters.length())));
                }

                String username = prefix + base.toString() + suffix;
                String password = generateRandomPassword();
                credentials.add(username + " : " + password);
                lastGeneratedCredentials.add(username + ":" + password);

                // حفظ في قاعدة البيانات
                saveToDatabase(username, password, "default");
            }

            // عرض النتائج
            StringBuilder output = new StringBuilder();
            for (String cred : credentials) {
                output.append(cred).append("\n");
            }
            outputText.setText(output.toString());

            // إضافة المستخدمين إلى الراوتر إذا كان متصلاً
            // if (isConnectedToRouter && connection != null) {
            //     new AddUsersToRouterTask().execute(lastGeneratedCredentials);
            // }

            Toast.makeText(this, "تم توليد " + count + " كرت بنجاح", Toast.LENGTH_SHORT).show();

        } catch (NumberFormatException e) {
            Toast.makeText(this, "يرجى إدخال قيم عددية صحيحة", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    // دالة لتوليد كلمة مرور عشوائية
    private String generateRandomPassword() {
        String characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder password = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 8; i++) {
            password.append(characters.charAt(random.nextInt(characters.length())));
        }
        return password.toString();
    }

    // دالة لعرض الكروت المحفوظة
    private void viewSavedCards() {
        Intent intent = new Intent(this, SavedCardsActivity.class);
        startActivity(intent);
    }

    // دالة لتصدير PDF
    private void exportToPdf() {
        if (lastGeneratedCredentials.isEmpty()) {
            Toast.makeText(this, "لا توجد كروت لتصديرها", Toast.LENGTH_SHORT).show();
            return;
        }
        new ExportPdfTask().execute(lastGeneratedCredentials);
    }

    // دالة لحفظ البيانات في قاعدة البيانات
    private void saveToDatabase(String username, String password, String profile) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_USERNAME, username);
        values.put(DatabaseHelper.COLUMN_PASSWORD, password);
        values.put(DatabaseHelper.COLUMN_PROFILE, profile);

        long newRowId = db.insert(DatabaseHelper.TABLE_CREDENTIALS, null, values);
        if (newRowId == -1) {
            Toast.makeText(this, "فشل في الحفظ في قاعدة البيانات", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (dbHelper != null) {
            dbHelper.close();
        }
        // if (connection != null) {
        //     try {
        //         connection.close();
        //     } catch (Exception e) {
        //         e.printStackTrace();
        //     }
        // }
    }

    // AsyncTask للاتصال بالراوتر
    private class ConnectToRouterTask extends AsyncTask<String, Void, Boolean> {
        private String errorMessage = "";

        @Override
        protected void onPreExecute() {
            connectButton.setText("جاري الاتصال...");
            connectButton.setEnabled(false);
        }

        @Override
        protected Boolean doInBackground(String... params) {
            try {
                // String ip = params[0];
                // String username = params[1];
                // String password = params[2];

                // connection = ApiConnection.connect(ip);
                // connection.login(username, password);

                // Simulate connection for demo purposes
                Thread.sleep(2000);
                return true;
            } catch (Exception e) {
                errorMessage = e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            connectButton.setEnabled(true);
            if (success) {
                isConnectedToRouter = true;
                connectButton.setText("متصل ✓");
                connectButton.setBackgroundColor(getResources().getColor(android.R.color.holo_green_light));
                Toast.makeText(MainActivity.this, "تم الاتصال بالراوتر بنجاح (وضع التجربة)", Toast.LENGTH_SHORT).show();
            } else {
                isConnectedToRouter = false;
                connectButton.setText("الاتصال بالراوتر");
                connectButton.setBackgroundColor(getResources().getColor(android.R.color.holo_red_light));
                Toast.makeText(MainActivity.this, "فشل الاتصال: " + errorMessage, Toast.LENGTH_LONG).show();
            }
        }
    }

    // AsyncTask لإضافة المستخدمين إلى الراوتر
    private class AddUsersToRouterTask extends AsyncTask<ArrayList<String>, Void, Boolean> {
        private String errorMessage = "";
        private int addedCount = 0;

        @Override
        protected Boolean doInBackground(ArrayList<String>... params) {
            try {
                ArrayList<String> credentials = params[0];
                for (String cred : credentials) {
                    String[] parts = cred.split(":");
                    if (parts.length == 2) {
                        // String username = parts[0];
                        // String password = parts[1];

                        // إضافة المستخدم إلى الراوتر
                        // List<String> command = connection.execute("/ppp/secret/add name=" + username + " password=" + password);
                        addedCount++;
                    }
                }
                return true;
            } catch (Exception e) {
                errorMessage = e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            if (success) {
                Toast.makeText(MainActivity.this, "تم إضافة " + addedCount + " مستخدم إلى الراوتر (وضع التجربة)", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(MainActivity.this, "فشل في إضافة المستخدمين: " + errorMessage, Toast.LENGTH_LONG).show();
            }
        }
    }

    // AsyncTask لتصدير PDF
    private class ExportPdfTask extends AsyncTask<ArrayList<String>, Void, Boolean> {
        private String filePath = "";
        private String errorMessage = "";

        @Override
        protected void onPreExecute() {
            exportPdfButton.setText("جاري التصدير...");
            exportPdfButton.setEnabled(false);
        }

        @Override
        protected Boolean doInBackground(ArrayList<String>... params) {
            try {
                ArrayList<String> credentials = params[0];

                // إنشاء مجلد التطبيق في التخزين الخارجي
                File appDir = new File(Environment.getExternalStorageDirectory(), "MikrotikCards");
                if (!appDir.exists()) {
                    appDir.mkdirs();
                }

                // إنشاء ملف PDF
                String fileName = "mikrotik_cards_" + System.currentTimeMillis() + ".pdf";
                File pdfFile = new File(appDir, fileName);
                filePath = pdfFile.getAbsolutePath();

                Document document = new Document(PageSize.A4);
                PdfWriter.getInstance(document, new FileOutputStream(pdfFile));
                document.open();

                // إضافة العنوان
                Font titleFont = new Font(Font.FontFamily.HELVETICA, 18, Font.BOLD);
                Paragraph title = new Paragraph("MikroTik User Cards", titleFont);
                title.setAlignment(Element.ALIGN_CENTER);
                document.add(title);
                document.add(new Paragraph("\n"));

                // إضافة الكروت
                Font cardFont = new Font(Font.FontFamily.COURIER, 12);
                for (String cred : credentials) {
                    String[] parts = cred.split(":");
                    if (parts.length == 2) {
                        Paragraph card = new Paragraph(
                            "Username: " + parts[0] + "\n" +
                            "Password: " + parts[1] + "\n" +
                            "------------------------\n", cardFont);
                        document.add(card);
                    }
                }

                document.close();
                return true;
            } catch (Exception e) {
                errorMessage = e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            exportPdfButton.setText("تصدير PDF");
            exportPdfButton.setEnabled(true);

            if (success) {
                Toast.makeText(MainActivity.this, "تم حفظ الملف في: " + filePath, Toast.LENGTH_LONG).show();
            } else {
                Toast.makeText(MainActivity.this, "فشل في تصدير PDF: " + errorMessage, Toast.LENGTH_LONG).show();
            }
        }
    }

    // كلاس مساعد لإدارة قاعدة البيانات
    public static class DatabaseHelper extends SQLiteOpenHelper {
        private static final String DATABASE_NAME = "MikroTikCards.db";
        private static final int DATABASE_VERSION = 1;

        public static final String TABLE_CREDENTIALS = "credentials";
        public static final String COLUMN_ID = "id";
        public static final String COLUMN_USERNAME = "username";
        public static final String COLUMN_PASSWORD = "password";
        public static final String COLUMN_PROFILE = "profile";

        private static final String TABLE_CREATE =
                "CREATE TABLE " + TABLE_CREDENTIALS + " (" +
                        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                        COLUMN_USERNAME + " TEXT NOT NULL, " +
                        COLUMN_PASSWORD + " TEXT, " +
                        COLUMN_PROFILE + " TEXT);";

        public DatabaseHelper(android.content.Context context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            db.execSQL(TABLE_CREATE);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            db.execSQL("DROP TABLE IF EXISTS " + TABLE_CREDENTIALS);
            onCreate(db);
        }
    }
}