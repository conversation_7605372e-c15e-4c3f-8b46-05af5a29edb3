1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.mikrotikcardgenerator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:5:5-81
12-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:5:22-78
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.mikrotikcardgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.mikrotikcardgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:7:5-26:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:8:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ef7928f296f3a9fc53086a6c41b89e4\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:debuggable="true"
24        android:extractNativeLibs="false"
25        android:icon="@mipmap/ic_launcher"
25-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:9:9-43
26        android:label="@string/app_name"
26-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:10:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:11:9-54
28        android:supportsRtl="true"
28-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:12:9-35
29        android:theme="@style/Theme.MikrotikCardGenerator" >
29-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:13:9-59
30        <activity
30-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:14:9-21:20
31            android:name="com.example.mikrotikcardgenerator.MainActivity"
31-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:15:13-41
32            android:exported="true" >
32-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:16:13-36
33            <intent-filter>
33-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:17:13-20:29
34                <action android:name="android.intent.action.MAIN" />
34-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:18:17-69
34-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:18:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:19:17-77
36-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:19:27-74
37            </intent-filter>
38        </activity>
39        <activity
39-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:22:9-25:58
40            android:name="com.example.mikrotikcardgenerator.SavedCardsActivity"
40-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:23:13-47
41            android:exported="false"
41-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:24:13-37
42            android:parentActivityName="com.example.mikrotikcardgenerator.MainActivity" />
42-->C:\Users\<USER>\MikrotikCardGenerator\app\src\main\AndroidManifest.xml:25:13-55
43
44        <provider
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
45            android:name="androidx.startup.InitializationProvider"
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
46            android:authorities="com.example.mikrotikcardgenerator.androidx-startup"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
47            android:exported="false" >
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
48            <meta-data
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.emoji2.text.EmojiCompatInitializer"
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
50                android:value="androidx.startup" />
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1f12bd27a31997bc69e31706a57cab\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07881218a44f82065948a6f2831db9b\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
52                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
52-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07881218a44f82065948a6f2831db9b\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
53                android:value="androidx.startup" />
53-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c07881218a44f82065948a6f2831db9b\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
54        </provider>
55    </application>
56
57</manifest>
