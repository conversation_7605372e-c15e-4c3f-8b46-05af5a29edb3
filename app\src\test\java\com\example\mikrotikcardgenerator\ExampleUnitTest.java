package com.example.mikrotikcardgenerator;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {
    @Test
    public void addition_isCorrect() {
        assertEquals(4, 2 + 2);
    }

    @Test
    public void testPasswordGeneration() {
        // Test that password generation logic would work
        String characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        assertTrue("Character set should not be empty", characters.length() > 0);
        assertEquals("Character set should have 62 characters", 62, characters.length());
    }

    @Test
    public void testUsernameConstruction() {
        // Test username construction logic
        String prefix = "user";
        String suffix = "01";
        String base = "abc123";
        String username = prefix + base + suffix;
        assertEquals("Username should be constructed correctly", "userabc12301", username);
    }

    @Test
    public void testCredentialSplit() {
        // Test credential string splitting
        String credential = "username:password";
        String[] parts = credential.split(":");
        assertEquals("Should split into 2 parts", 2, parts.length);
        assertEquals("First part should be username", "username", parts[0]);
        assertEquals("Second part should be password", "password", parts[1]);
    }
}
