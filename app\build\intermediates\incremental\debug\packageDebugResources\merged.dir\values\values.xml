<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="cred_types">
        <item>أرقام وحروف</item>
        <item>أرقام فقط</item>
        <item>حروف فقط</item>
    </string-array>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">MikrotikCardGenerator</string>
    <string name="connect_to_router">الاتصال بالراوتر</string>
    <string name="count_hint">عدد الكروت</string>
    <string name="cred_type_hint">نوع البيانات</string>
    <string name="export_pdf">تصدير PDF</string>
    <string name="generate_cards">توليد الكروت</string>
    <string name="length_hint">طول الكود</string>
    <string name="output_label">النتائج:</string>
    <string name="prefix_hint">البادئة</string>
    <string name="router_ip_hint">عنوان IP للراوتر</string>
    <string name="router_password_hint">كلمة مرور الراوتر</string>
    <string name="router_username_hint">اسم مستخدم الراوتر</string>
    <string name="suffix_hint">اللاحقة</string>
    <string name="view_saved_cards">عرض الكروت المحفوظة</string>
    <style name="Theme.MikrotikCardGenerator" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>