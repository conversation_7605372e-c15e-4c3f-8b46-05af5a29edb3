package com.example.mikrotikcardgenerator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * فئة API وهمية لمحاكاة الاتصال براوتر MikroTik
 * Mock MikroTik API class for simulating router connection
 */
public class MockMikrotikApi {
    
    private boolean isConnected = false;
    private String routerIp;
    private String username;
    private String password;
    private List<Map<String, String>> pppSecrets;
    private Random random;
    
    public MockMikrotikApi() {
        this.pppSecrets = new ArrayList<>();
        this.random = new Random();
    }
    
    /**
     * محاكاة الاتصال بالراوتر
     * Simulate connection to router
     */
    public boolean connect(String ip, String username, String password) throws Exception {
        // محاكاة تأخير الشبكة
        Thread.sleep(1000 + random.nextInt(2000)); // 1-3 ثانية
        
        this.routerIp = ip;
        this.username = username;
        this.password = password;
        
        // محاكاة فشل الاتصال أحياناً (10% احتمال)
        if (random.nextInt(10) == 0) {
            throw new Exception("فشل في الاتصال: تحقق من عنوان IP أو بيانات الاعتماد");
        }
        
        // محاكاة التحقق من بيانات الاعتماد
        if (username.isEmpty() || password.isEmpty()) {
            throw new Exception("اسم المستخدم أو كلمة المرور فارغة");
        }
        
        if (!isValidIp(ip)) {
            throw new Exception("عنوان IP غير صحيح");
        }
        
        this.isConnected = true;
        
        // إضافة بعض المستخدمين الوهميين الموجودين مسبقاً
        initializeMockUsers();
        
        return true;
    }
    
    /**
     * قطع الاتصال
     * Disconnect from router
     */
    public void disconnect() {
        this.isConnected = false;
        this.routerIp = null;
        this.username = null;
        this.password = null;
    }
    
    /**
     * التحقق من حالة الاتصال
     * Check connection status
     */
    public boolean isConnected() {
        return isConnected;
    }
    
    /**
     * إضافة مستخدم PPP جديد
     * Add new PPP user
     */
    public boolean addPppUser(String username, String password, String profile) throws Exception {
        if (!isConnected) {
            throw new Exception("غير متصل بالراوتر");
        }
        
        // محاكاة تأخير العملية
        Thread.sleep(200 + random.nextInt(300));
        
        // التحقق من وجود المستخدم مسبقاً
        for (Map<String, String> user : pppSecrets) {
            if (user.get("name").equals(username)) {
                throw new Exception("المستخدم موجود مسبقاً: " + username);
            }
        }
        
        // إضافة المستخدم الجديد
        Map<String, String> newUser = new HashMap<>();
        newUser.put("id", String.valueOf(pppSecrets.size() + 1));
        newUser.put("name", username);
        newUser.put("password", password);
        newUser.put("profile", profile != null ? profile : "default");
        newUser.put("disabled", "false");
        newUser.put("comment", "تم إنشاؤه بواسطة MikroTik Card Generator");
        
        pppSecrets.add(newUser);
        
        return true;
    }
    
    /**
     * إضافة عدة مستخدمين دفعة واحدة
     * Add multiple users at once
     */
    public int addMultiplePppUsers(List<String> credentials, String profile) throws Exception {
        if (!isConnected) {
            throw new Exception("غير متصل بالراوتر");
        }
        
        int addedCount = 0;
        List<String> failedUsers = new ArrayList<>();
        
        for (String cred : credentials) {
            String[] parts = cred.split(":");
            if (parts.length == 2) {
                try {
                    addPppUser(parts[0], parts[1], profile);
                    addedCount++;
                } catch (Exception e) {
                    failedUsers.add(parts[0] + " (" + e.getMessage() + ")");
                }
            }
        }
        
        if (!failedUsers.isEmpty()) {
            throw new Exception("فشل في إضافة " + failedUsers.size() + " مستخدم: " + 
                              String.join(", ", failedUsers));
        }
        
        return addedCount;
    }
    
    /**
     * الحصول على قائمة المستخدمين
     * Get list of users
     */
    public List<Map<String, String>> getPppUsers() throws Exception {
        if (!isConnected) {
            throw new Exception("غير متصل بالراوتر");
        }
        
        // محاكاة تأخير الاستعلام
        Thread.sleep(300 + random.nextInt(500));
        
        return new ArrayList<>(pppSecrets);
    }
    
    /**
     * حذف مستخدم
     * Delete user
     */
    public boolean deletePppUser(String username) throws Exception {
        if (!isConnected) {
            throw new Exception("غير متصل بالراوتر");
        }
        
        // محاكاة تأخير العملية
        Thread.sleep(200 + random.nextInt(300));
        
        for (int i = 0; i < pppSecrets.size(); i++) {
            if (pppSecrets.get(i).get("name").equals(username)) {
                pppSecrets.remove(i);
                return true;
            }
        }
        
        throw new Exception("المستخدم غير موجود: " + username);
    }
    
    /**
     * الحصول على معلومات الراوتر
     * Get router information
     */
    public Map<String, String> getRouterInfo() throws Exception {
        if (!isConnected) {
            throw new Exception("غير متصل بالراوتر");
        }
        
        // محاكاة تأخير الاستعلام
        Thread.sleep(500 + random.nextInt(1000));
        
        Map<String, String> info = new HashMap<>();
        info.put("board-name", "RB750Gr3");
        info.put("model", "RouterBOARD 750Gr3");
        info.put("version", "6.49.6 (stable)");
        info.put("build-time", "Aug/26/2022 13:18:15");
        info.put("uptime", "2w3d4h15m30s");
        info.put("cpu-load", random.nextInt(50) + "%");
        info.put("free-memory", (512 + random.nextInt(256)) + " MB");
        info.put("total-memory", "1024 MB");
        info.put("architecture-name", "arm");
        
        return info;
    }
    
    /**
     * الحصول على إحصائيات الشبكة
     * Get network statistics
     */
    public Map<String, Object> getNetworkStats() throws Exception {
        if (!isConnected) {
            throw new Exception("غير متصل بالراوتر");
        }
        
        Thread.sleep(300 + random.nextInt(500));
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("active-users", pppSecrets.size());
        stats.put("total-users", pppSecrets.size());
        stats.put("cpu-load", random.nextInt(50));
        stats.put("memory-usage", random.nextInt(70) + 30);
        stats.put("uptime-seconds", random.nextInt(1000000) + 86400);
        
        return stats;
    }
    
    // دوال مساعدة
    private boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) return false;
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) return false;
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) return false;
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private void initializeMockUsers() {
        // إضافة بعض المستخدمين الوهميين
        String[] mockUsers = {
            "admin:admin123:default",
            "user1:pass123:default", 
            "test:test123:default"
        };
        
        for (String userInfo : mockUsers) {
            String[] parts = userInfo.split(":");
            Map<String, String> user = new HashMap<>();
            user.put("id", String.valueOf(pppSecrets.size() + 1));
            user.put("name", parts[0]);
            user.put("password", parts[1]);
            user.put("profile", parts[2]);
            user.put("disabled", "false");
            user.put("comment", "مستخدم موجود مسبقاً");
            
            pppSecrets.add(user);
        }
    }
}
