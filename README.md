# MikroTik Card Generator

تطبيق Android لتوليد كروت مستخدمين لراوترات MikroTik مع إمكانية حفظها في قاعدة البيانات وتصديرها كملف PDF.

## المميزات

### ✅ المميزات المكتملة:
- **توليد كروت المستخدمين**: إنشاء أسماء مستخدمين وكلمات مرور عشوائية
- **أنواع البيانات المختلفة**: أرقام فقط، حروف فقط، أو أرقام وحروف
- **قاعدة البيانات المحلية**: حفظ جميع الكروت المولدة في قاعدة بيانات SQLite
- **تصدير PDF**: إنشاء ملف PDF يحتوي على جميع الكروت المولدة
- **عرض الكروت المحفوظة**: استعراض جميع الكروت المحفوظة سابقاً
- **واجهة مستخدم عربية**: دعم كامل للغة العربية
- **تصميم متجاوب**: واجهة مستخدم سهلة الاستخدام

### 🚧 المميزات قيد التطوير:
- **الاتصال بالراوتر**: حالياً في وضع التجربة (يحتاج إلى مكتبة MikroTik API)
- **إضافة المستخدمين للراوتر**: سيتم تفعيلها عند إضافة مكتبة MikroTik

## متطلبات النظام

- Android 8.0 (API level 26) أو أحدث
- مساحة تخزين لحفظ ملفات PDF

## كيفية الاستخدام

### 1. إعدادات الراوتر
- أدخل عنوان IP للراوتر
- أدخل اسم المستخدم وكلمة المرور للراوتر
- اضغط على "الاتصال بالراوتر" (حالياً في وضع التجربة)

### 2. توليد الكروت
- أدخل البادئة واللاحقة (اختياري)
- حدد طول الكود (3 أحرف على الأقل)
- أدخل عدد الكروت المطلوبة (1-1000)
- اختر نوع البيانات من القائمة المنسدلة
- اضغط على "توليد الكروت"

### 3. إدارة الكروت
- **عرض الكروت المحفوظة**: استعراض جميع الكروت المحفوظة
- **تصدير PDF**: حفظ الكروت المولدة كملف PDF في مجلد `MikrotikCards`

## البنية التقنية

### التقنيات المستخدمة:
- **Java**: لغة البرمجة الأساسية
- **Android SDK**: إطار العمل
- **SQLite**: قاعدة البيانات المحلية
- **iText PDF**: مكتبة إنشاء ملفات PDF
- **AsyncTask**: للعمليات غير المتزامنة

### هيكل المشروع:
```
app/src/main/
├── java/com/example/mikrotikcardgenerator/
│   ├── MainActivity.java          # النشاط الرئيسي
│   └── SavedCardsActivity.java    # نشاط عرض الكروت المحفوظة
├── res/
│   ├── layout/
│   │   ├── activity_main.xml      # تخطيط الشاشة الرئيسية
│   │   └── activity_saved_cards.xml # تخطيط شاشة الكروت المحفوظة
│   └── values/
│       └── strings.xml            # النصوص والموارد
└── AndroidManifest.xml            # إعدادات التطبيق
```

## قاعدة البيانات

### جدول `credentials`:
- `id`: المعرف الفريد (INTEGER PRIMARY KEY)
- `username`: اسم المستخدم (TEXT NOT NULL)
- `password`: كلمة المرور (TEXT)
- `profile`: الملف الشخصي (TEXT)

## الأذونات المطلوبة

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

## التطوير المستقبلي

### المميزات المخططة:
1. **إضافة مكتبة MikroTik API**: لتفعيل الاتصال الحقيقي بالراوتر
2. **إدارة الملفات الشخصية**: إضافة ملفات شخصية مختلفة للمستخدمين
3. **تصدير تنسيقات أخرى**: Excel, CSV
4. **إعدادات متقدمة**: تخصيص قوالب الكروت
5. **نسخ احتياطي**: رفع البيانات للسحابة
6. **إحصائيات**: تقارير عن استخدام الكروت

## كيفية البناء

1. استنساخ المشروع:
```bash
git clone [repository-url]
cd MikrotikCardGenerator
```

2. بناء التطبيق:
```bash
./gradlew assembleDebug
```

3. تثبيت التطبيق:
```bash
./gradlew installDebug
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. إنشاء Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير. بعض المميزات قد تحتاج إلى تحسينات إضافية.
