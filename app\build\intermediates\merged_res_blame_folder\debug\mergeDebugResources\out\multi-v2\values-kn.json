{"logs": [{"outputFile": "com.example.mikrotikcardgenerator.app-mergeDebugResources-26:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7d8d788490da515ed5703e42d0a5a29\\transformed\\material-1.9.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,353,436,518,633,728,835,948,1033,1096,1190,1256,1318,1421,1487,1558,1617,1693,1758,1812,1925,1983,2044,2098,2177,2293,2376,2467,2609,2688,2767,2855,2912,2964,3030,3110,3200,3284,3361,3438,3515,3584,3683,3760,3853,3948,4022,4103,4199,4250,4318,4404,4492,4555,4620,4683,4788,4891,4986,5091,5152,5208", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,87,56,51,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,60,55,81", "endOffsets": "264,348,431,513,628,723,830,943,1028,1091,1185,1251,1313,1416,1482,1553,1612,1688,1753,1807,1920,1978,2039,2093,2172,2288,2371,2462,2604,2683,2762,2850,2907,2959,3025,3105,3195,3279,3356,3433,3510,3579,3678,3755,3848,3943,4017,4098,4194,4245,4313,4399,4487,4550,4615,4678,4783,4886,4981,5086,5147,5203,5285"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3073,3157,3240,3322,3437,3532,3639,3752,3837,3900,3994,4060,4122,4225,4291,4362,4421,4497,4562,4616,4729,4787,4848,4902,4981,5097,5180,5271,5413,5492,5571,5659,5716,5768,5834,5914,6004,6088,6165,6242,6319,6388,6487,6564,6657,6752,6826,6907,7003,7054,7122,7208,7296,7359,7424,7487,7592,7695,7790,7895,7956,8012", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,87,56,51,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,60,55,81", "endOffsets": "314,3152,3235,3317,3432,3527,3634,3747,3832,3895,3989,4055,4117,4220,4286,4357,4416,4492,4557,4611,4724,4782,4843,4897,4976,5092,5175,5266,5408,5487,5566,5654,5711,5763,5829,5909,5999,6083,6160,6237,6314,6383,6482,6559,6652,6747,6821,6902,6998,7049,7117,7203,7291,7354,7419,7482,7587,7690,7785,7890,7951,8007,8089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1cf9ab8460bf2fdf2e92c9407230975\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,433,545,658,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,8094", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "428,540,653,741,848,974,1052,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2147,2263,2373,2472,2585,2690,2804,2968,3068,8172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ef7928f296f3a9fc53086a6c41b89e4\\transformed\\core-1.9.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "8177", "endColumns": "100", "endOffsets": "8273"}}]}]}