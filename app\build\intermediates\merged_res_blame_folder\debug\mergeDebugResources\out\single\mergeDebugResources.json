[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\layout_activity_saved_cards.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\layout\\activity_saved_cards.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\xml\\backup_rules.xml"}, {"merged": "com.example.mikrotikcardgenerator.app-debug-28:/layout_activity_main.xml.flat", "source": "com.example.mikrotikcardgenerator.app-main-30:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-debug-28:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.mikrotikcardgenerator.app-main-30:\\mipmap-hdpi\\ic_launcher.webp"}]