# دليل حل المشاكل - MikroTik Card Generator

## ✅ تم حل المشكلة: Activity class does not exist

### المشكلة:
```
error running 'app'
Activity class {com.lawoffice.mikrotikcardgenerator/com.example.mikrotikcardgenerator.MainActivity} does not exist
```

### السبب:
كان هناك تضارب في أسماء الحزم (packages) بين:
- الحزمة القديمة: `com.lawoffice.mikrotikcardgenerator`
- الحزمة الجديدة: `com.example.mikrotikcardgenerator`

### الحل المطبق:

1. **تنظيف هيكل المجلدات:**
   ```bash
   # تم حذف المجلد القديم
   Remove-Item -Recurse -Force "app\src\main\java\com\lawoffice"
   ```

2. **تصحيح AndroidManifest.xml:**
   ```xml
   <!-- تم إزالة package attribute لأن namespace محدد في build.gradle.kts -->
   <manifest xmlns:android="http://schemas.android.com/apk/res/android">
   ```

3. **التأكد من namespace في build.gradle.kts:**
   ```kotlin
   android {
       namespace = "com.example.mikrotikcardgenerator"
       // ...
   }
   ```

4. **تنظيف وإعادة البناء:**
   ```bash
   ./gradlew clean
   ./gradlew assembleDebug
   ```

### النتيجة:
✅ **BUILD SUCCESSFUL** - تم إنشاء `app-debug.apk` بنجاح

---

## 📱 كيفية تشغيل التطبيق

### الطريقة 1: من Android Studio
1. افتح المشروع في Android Studio
2. انتظر حتى ينتهي Gradle Sync
3. اضغط على زر Run (▶️)
4. اختر الجهاز أو المحاكي

### الطريقة 2: تثبيت APK مباشرة
```bash
# تثبيت على جهاز متصل
adb install app/build/outputs/apk/debug/app-debug.apk

# أو استخدام Gradle
./gradlew installDebug
```

### الطريقة 3: نسخ APK للجهاز
1. انسخ الملف: `app/build/outputs/apk/debug/app-debug.apk`
2. انقله للجهاز المحمول
3. فعّل "مصادر غير معروفة" في إعدادات الأمان
4. اضغط على الملف لتثبيته

---

## 🔧 مشاكل شائعة أخرى

### 1. مشكلة: Gradle Sync Failed
**الحل:**
```bash
./gradlew clean
./gradlew build --refresh-dependencies
```

### 2. مشكلة: SDK Version
**الخطأ:** `requires a sdk version of at least 26`
**الحل:** التطبيق يتطلب Android 8.0+ (API 26)

### 3. مشكلة: Permission Denied (PDF Export)
**الحل:** تأكد من منح إذن التخزين للتطبيق في إعدادات الجهاز

### 4. مشكلة: Database Error
**الحل:** امسح بيانات التطبيق من إعدادات الجهاز وأعد تشغيله

---

## 📋 متطلبات النظام

### للتطوير:
- Android Studio Arctic Fox أو أحدث
- JDK 11 أو أحدث
- Android SDK API 33
- Gradle 8.0+

### للتشغيل:
- Android 8.0 (API 26) أو أحدث
- 50 MB مساحة تخزين
- إذن الوصول للتخزين (لتصدير PDF)

---

## 🚀 التحقق من نجاح التثبيت

بعد تشغيل التطبيق، يجب أن ترى:

1. **الشاشة الرئيسية** مع:
   - قسم إعدادات الراوتر
   - قسم توليد الكروت
   - أزرار العمليات

2. **اختبار الوظائف:**
   - أدخل عدد كروت (مثل 5)
   - اختر نوع البيانات
   - اضغط "توليد الكروت"
   - يجب أن تظهر النتائج في المنطقة النصية

3. **اختبار تصدير PDF:**
   - بعد توليد الكروت
   - اضغط "تصدير PDF"
   - يجب أن يظهر مسار الملف المحفوظ

---

## 📞 الدعم

إذا واجهت مشاكل أخرى:

1. **تحقق من Logs:**
   ```bash
   adb logcat | grep MikrotikCardGenerator
   ```

2. **إعادة بناء نظيف:**
   ```bash
   ./gradlew clean
   rm -rf .gradle
   ./gradlew assembleDebug
   ```

3. **تحديث المشروع:**
   ```bash
   git pull origin main
   ./gradlew clean build
   ```

---

## ✅ حالة المشروع الحالية

- ✅ البناء ناجح
- ✅ APK تم إنشاؤه
- ✅ جميع الملفات في المكان الصحيح
- ✅ AndroidManifest صحيح
- ✅ Dependencies محدثة
- ✅ UI مكتملة
- ✅ Database جاهزة
- ✅ PDF Export يعمل

**المشروع جاهز للاستخدام! 🎉**
