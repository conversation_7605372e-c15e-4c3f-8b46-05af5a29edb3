plugins {
    id 'com.android.application'
}

android {
    namespace 'com.example.mikrotikcardgenerator' // Add this line
    compileSdk 33
    defaultConfig {
        applicationId "com.example.mikrotikcardgenerator"
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.itextpdf:itextpdf:5.5.13.3'
    implementation 'me.legrange:java-mikrotik:3.0.5'
}