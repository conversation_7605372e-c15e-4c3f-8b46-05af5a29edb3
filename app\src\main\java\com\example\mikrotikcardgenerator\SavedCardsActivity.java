package com.example.mikrotikcardgenerator;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;

public class SavedCardsActivity extends AppCompatActivity {

    private ListView cardsListView;
    private MainActivity.DatabaseHelper dbHelper;
    private ArrayList<String> cardsList;
    private ArrayAdapter<String> adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_saved_cards);

        cardsListView = findViewById(R.id.cards_list_view);
        dbHelper = new MainActivity.DatabaseHelper(this);
        cardsList = new ArrayList<>();

        loadSavedCards();

        adapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1, cardsList);
        cardsListView.setAdapter(adapter);
    }

    private void loadSavedCards() {
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        String[] projection = {
            MainActivity.DatabaseHelper.COLUMN_USERNAME,
            MainActivity.DatabaseHelper.COLUMN_PASSWORD,
            MainActivity.DatabaseHelper.COLUMN_PROFILE
        };

        Cursor cursor = db.query(
            MainActivity.DatabaseHelper.TABLE_CREDENTIALS,
            projection,
            null,
            null,
            null,
            null,
            MainActivity.DatabaseHelper.COLUMN_ID + " DESC"
        );

        cardsList.clear();
        while (cursor.moveToNext()) {
            String username = cursor.getString(cursor.getColumnIndexOrThrow(MainActivity.DatabaseHelper.COLUMN_USERNAME));
            String password = cursor.getString(cursor.getColumnIndexOrThrow(MainActivity.DatabaseHelper.COLUMN_PASSWORD));
            String profile = cursor.getString(cursor.getColumnIndexOrThrow(MainActivity.DatabaseHelper.COLUMN_PROFILE));

            cardsList.add("Username: " + username + "\nPassword: " + password + "\nProfile: " + profile);
        }
        cursor.close();

        if (cardsList.isEmpty()) {
            Toast.makeText(this, "لا توجد كروت محفوظة", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (dbHelper != null) {
            dbHelper.close();
        }
    }
}
