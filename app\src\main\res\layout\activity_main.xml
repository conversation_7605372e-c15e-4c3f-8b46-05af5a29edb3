<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <EditText
        android:id="@+id/router_ip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Router IP" />

    <EditText
        android:id="@+id/router_username"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Router Username" />

    <EditText
        android:id="@+id/router_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Router Password"
        android:inputType="textPassword" />

    <Button
        android:id="@+id/connect_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Connect to Router" />

    <EditText
        android:id="@+id/card_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Number of Cards"
        android:inputType="number" />

    <EditText
        android:id="@+id/username_prefix"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Username Prefix" />

    <Button
        android:id="@+id/generate_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Generate Cards" />
</LinearLayout>