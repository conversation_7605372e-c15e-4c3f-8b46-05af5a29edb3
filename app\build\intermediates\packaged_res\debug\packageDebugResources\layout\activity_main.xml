<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Router Connection Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="إعدادات الراوتر"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/router_ip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/router_ip_hint"
            android:inputType="textUri"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/router_username"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/router_username_hint"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/router_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/router_password_hint"
            android:inputType="textPassword"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/connect_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/connect_to_router"
            android:layout_marginBottom="16dp" />

        <!-- Card Generation Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="إعدادات توليد الكروت"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/prefix_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/prefix_hint"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/suffix_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/suffix_hint"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/length_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/length_hint"
            android:inputType="number"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/count_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/count_hint"
            android:inputType="number"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/cred_type_spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/generate_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/generate_cards"
            android:layout_marginBottom="16dp" />

        <!-- Output Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/output_label"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/output_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:textIsSelectable="true"
            android:minHeight="100dp"
            android:gravity="top"
            android:layout_marginBottom="16dp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/view_saved_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/view_saved_cards"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/export_pdf_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/export_pdf"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>