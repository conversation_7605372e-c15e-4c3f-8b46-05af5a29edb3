package com.example.mikrotikcardgenerator;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;
import java.util.Random;

public class MainActivity extends AppCompatActivity {

    private EditText prefixInput, suffixInput, lengthInput, countInput;
    private Spinner credTypeSpinner;
    private Button generateButton;
    private TextView outputText;
    private DatabaseHelper dbHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // ربط عناصر الواجهة
        prefixInput = findViewById(R.id.prefix_input);
        suffixInput = findViewById(R.id.suffix_input);
        lengthInput = findViewById(R.id.length_input);
        countInput = findViewById(R.id.count_input);
        credTypeSpinner = findViewById(R.id.cred_type_spinner);
        generateButton = findViewById(R.id.generate_button);
        outputText = findViewById(R.id.output_text);

        // إعداد قاعدة البيانات
        dbHelper = new DatabaseHelper(this);

        // إعداد القائمة المنسدلة لنوع البيانات
        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                R.array.cred_types, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        credTypeSpinner.setAdapter(adapter);

        // إعداد زر التوليد
        generateButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                generateCredentials();
            }
        });
    }

    // دالة لتوليد بيانات الاعتماد
    private void generateCredentials() {
        try {
            String prefix = prefixInput.getText().toString().trim();
            String suffix = suffixInput.getText().toString().trim();
            int length = Integer.parseInt(lengthInput.getText().toString());
            int count = Integer.parseInt(countInput.getText().toString());
            String credType = credTypeSpinner.getSelectedItem().toString();

            if (length < 5 || count < 1) {
                Toast.makeText(this, "يرجى إدخال قيم صحيحة", Toast.LENGTH_SHORT).show();
                return;
            }

            ArrayList<String> credentials = new ArrayList<>();
            Random random = new Random();

            for (int i = 0; i < count; i++) {
                String characters;
                switch (credType) {
                    case "أرقام فقط":
                        characters = "0123456789";
                        break;
                    case "حروف فقط":
                        characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
                        break;
                    default: // أرقام وحروف
                        characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
                        break;
                }

                StringBuilder base = new StringBuilder();
                for (int j = 0; j < length; j++) {
                    base.append(characters.charAt(random.nextInt(characters.length())));
                }

                String username = prefix + base.toString() + suffix;
                credentials.add(username);

                // حفظ في قاعدة البيانات
                saveToDatabase(username, "default_password", "default_profile");
            }

            // عرض النتائج
            StringBuilder output = new StringBuilder();
            for (String cred : credentials) {
                output.append(cred).append("\n");
            }
            outputText.setText(output.toString());

            Toast.makeText(this, "تم توليد " + count + " حساب بنجاح", Toast.LENGTH_SHORT).show();

        } catch (NumberFormatException e) {
            Toast.makeText(this, "يرجى إدخال قيم عددية صحيحة", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    // دالة لحفظ البيانات في قاعدة البيانات
    private void saveToDatabase(String username, String password, String profile) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(DatabaseHelper.COLUMN_USERNAME, username);
        values.put(DatabaseHelper.COLUMN_PASSWORD, password);
        values.put(DatabaseHelper.COLUMN_PROFILE, profile);

        long newRowId = db.insert(DatabaseHelper.TABLE_CREDENTIALS, null, values);
        if (newRowId == -1) {
            Toast.makeText(this, "فشل في الحفظ في قاعدة البيانات", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        dbHelper.close();
    }

    // كلاس مساعد لإدارة قاعدة البيانات
    public static class DatabaseHelper extends SQLiteOpenHelper {
        private static final String DATABASE_NAME = "MikroTikCards.db";
        private static final int DATABASE_VERSION = 1;

        public static final String TABLE_CREDENTIALS = "credentials";
        public static final String COLUMN_ID = "id";
        public static final String COLUMN_USERNAME = "username";
        public static final String COLUMN_PASSWORD = "password";
        public static final String COLUMN_PROFILE = "profile";

        private static final String TABLE_CREATE =
                "CREATE TABLE " + TABLE_CREDENTIALS + " (" +
                        COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                        COLUMN_USERNAME + " TEXT NOT NULL, " +
                        COLUMN_PASSWORD + " TEXT, " +
                        COLUMN_PROFILE + " TEXT);";

        public DatabaseHelper(MainActivity context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            db.execSQL(TABLE_CREATE);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            db.execSQL("DROP TABLE IF EXISTS " + TABLE_CREDENTIALS);
            onCreate(db);
        }
    }
}